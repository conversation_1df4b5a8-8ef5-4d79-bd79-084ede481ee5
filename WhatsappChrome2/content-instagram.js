// Content script injetado nas páginas do Instagram
// Faz a ponte entre o Instagram e a extensão PromoKit

/**
 * Busca dados do usuário via API do Instagram
 */
function buscarDadosInstagramAPI(username) {
  console.log('Buscando dados para usuário:', username);
  
  return fetch(
    `https://i.instagram.com/api/v1/users/web_profile_info/?username=${username}`,
    {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
        "X-IG-App-ID": "936619743392459",
        "Accept": "*/*",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site"
      }
    }
  )
    .then(r => {
      console.log('Resposta da API recebida para', username);
      return r.json();
    })
    .then(json => {
      console.log('Dados do Instagram para', username, ':', json);
      
      const responseData = {
        tipo: 'INSTAGRAM_DATA_RESPONSE',
        username: username,
        data: json,
        timestamp: new Date().toISOString()
      };
      
      // Envia dados de volta via postMessage para a página
      window.postMessage(responseData, '*');
      
      // Envia dados para o iframe PromoKit
      const iframe = document.getElementById('promokit-iframe');
      if (iframe && iframe.contentWindow) {
        try {
          iframe.contentWindow.postMessage(responseData, '*');
          console.log('Dados enviados para iframe PromoKit:', responseData);
        } catch (error) {
          console.error('Erro ao enviar dados para iframe PromoKit:', error);
        }
      } else {
        console.warn('Iframe PromoKit não encontrado ou não carregado');
      }
      
      return json;
    })
    .catch(err => {
      console.error('Erro ao buscar dados do Instagram para', username, ':', err);
      
      const errorData = {
        tipo: 'INSTAGRAM_DATA_ERROR',
        username: username,
        error: err.message,
        timestamp: new Date().toISOString()
      };
      
      // Envia erro de volta para a página
      window.postMessage(errorData, '*');
      
      // Envia erro para o iframe PromoKit
      const iframe = document.getElementById('promokit-iframe');
      if (iframe && iframe.contentWindow) {
        try {
          iframe.contentWindow.postMessage(errorData, '*');
          console.log('Erro enviado para iframe PromoKit:', errorData);
        } catch (error) {
          console.error('Erro ao enviar erro para iframe PromoKit:', error);
        }
      }
    });
}


// Configuração para controle do iframe
const configIG = {
  initialized: false,
  iframeInjected: false,
  currentUrl: location.href,
  currentUsername: null // Armazena o username atual para evitar mudanças desnecessárias
};

/**
 * Sistema robusto de detecção de mudança de URL no Instagram
 */
function setupUrlChangeDetector() {
  let lastUrl = location.href;
  let isDetecting = true;
  
  const handleUrlChange = () => {
    if (!isDetecting) return;
    
    const currentUrl = location.href;
    if (currentUrl !== lastUrl) {
      console.log('URL mudou de:', lastUrl, 'para:', currentUrl);
      lastUrl = currentUrl;
      configIG.currentUrl = currentUrl;
      
      // Atualiza contexto e iframe
      onInstagramUrlChange(currentUrl);
    }
  };

  // Método 1: Intercepta pushState e replaceState
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function() {
    originalPushState.apply(history, arguments);
    setTimeout(handleUrlChange, 50);
  };

  history.replaceState = function() {
    originalReplaceState.apply(history, arguments);
    setTimeout(handleUrlChange, 50);
  };

  // Método 2: Detecta navegação via botões voltar/avançar
  window.addEventListener('popstate', () => {
    setTimeout(handleUrlChange, 50);
  });

  // Método 3: Polling da URL (fallback mais confiável)
  setInterval(() => {
    handleUrlChange();
  }, 500);

  // Método 4: Observa mudanças no título da página
  const titleObserver = new MutationObserver(() => {
    setTimeout(handleUrlChange, 100);
  });

  if (document.querySelector('title')) {
    titleObserver.observe(document.querySelector('title'), {
      childList: true,
      subtree: true
    });
  }

  // Método 5: Observa mudanças no DOM principal (indica navegação)
  const mainObserver = new MutationObserver((mutations) => {
    // Verifica se houve mudanças significativas que indicam navegação
    const hasSignificantChanges = mutations.some(mutation => {
      return mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0;
    });
    
    if (hasSignificantChanges) {
      setTimeout(handleUrlChange, 100);
    }
  });

  // Observa o container principal do Instagram
  const mainContainer = document.querySelector('[role="main"]') || 
                       document.querySelector('#react-root') || 
                       document.body;
  
  if (mainContainer) {
    mainObserver.observe(mainContainer, {
      childList: true,
      subtree: false // Apenas mudanças diretas no container principal
    });
  }

  // Método 6: Intercepta cliques em links
  document.addEventListener('click', (event) => {
    const target = event.target.closest('a[href]');
    if (target && target.href) {
      // Verifica se é uma navegação interna do Instagram
      const url = new URL(target.href);
      if (url.hostname === location.hostname) {
        setTimeout(handleUrlChange, 200); // Delay maior para cliques
      }
    }
  }, true);

  // Método 7: Escuta eventos customizados do Instagram (se existirem)
  window.addEventListener('instagram:navigate', () => {
    setTimeout(handleUrlChange, 50);
  });

  console.log('Sistema robusto de detecção de URL do Instagram configurado');
  
  // Função para parar/resumir detecção se necessário
  window.instagramUrlDetector = {
    stop: () => { isDetecting = false; },
    start: () => { isDetecting = true; },
    forceCheck: handleUrlChange
  };
}

/**
 * Função para enviar mensagem para o PromoKit (similar ao WhatsApp)
 */
function envieParaPromokit(tipo, dados) {
  try {
    // Envia a mensagem para o window principal
    window.postMessage({
      tipo: tipo,
      ...dados
    }, '*');
    console.log(`[INSTAGRAM] Mensagem ${tipo} enviada via window.postMessage`);

    // Também tenta enviar para o iframe se existir
    const iframe = document.getElementById('promokit-iframe');
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage({
        tipo: tipo,
        ...dados
      }, '*');
      console.log(`[INSTAGRAM] Mensagem ${tipo} também enviada para o iframe`);
    }

    return true;
  } catch (e) {
    console.error(`[INSTAGRAM] Erro ao enviar mensagem ${tipo}`, e);
    return false;
  }
}

/**
 * Processa mudanças de URL no Instagram
 */
function onInstagramUrlChange(newUrl) {
  console.log('Processando mudança de URL:', newUrl);

  // Analisa a URL atual
  const urlInfo = analyzeInstagramUrl(newUrl);
  console.log('Informações da URL:', urlInfo);

  // Atualiza contexto baseado na URL
  updateContextFromUrl(urlInfo);

  // Se for um perfil de usuário, envia evento SELECIONOU_CONTATO
  if (urlInfo.isProfile && urlInfo.username && !urlInfo.isDirectMessage) {
    // Usa o username como telefone (já que Instagram não tem telefone)
    // e tenta extrair o nome do perfil da página
    const telefone = urlInfo.username;
    const nome = urlInfo.username; // Por padrão usa o username, mas pode ser melhorado
    const url = urlInfo.fullUrl;

    envieParaPromokit('SELECIONOU_CONTATO', {
      telefone: telefone,
      nome: nome,
      url: url,
      extensao: 'vendas'
    });

    console.log('Evento SELECIONOU_CONTATO enviado para:', urlInfo.username);
  }

  // Log da mudança para debug
  console.log('Página do Instagram alterada:', urlInfo.pathname);
}

/**
 * Analisa a URL do Instagram e extrai informações relevantes
 */
function analyzeInstagramUrl(url) {
  const urlObj = new URL(url);
  const pathname = urlObj.pathname;
  
  // Lista de URLs conhecidas que não são perfis de usuário
  const nonProfilePaths = [
    'explore', 'reels', 'stories', 'accounts', 'help', 'about', 
    'privacy', 'terms', 'support', 'press', 'api', 'developer',
    'business', 'creators', 'topics', 'directory', 'emails'
  ];
  
  // Extrai o primeiro segmento da URL (possível username)
  const firstSegment = pathname.split('/')[1];
  
  // Verifica se é um perfil de usuário real
  const isUserProfile = () => {
    // Não é perfil se está vazio ou é uma URL conhecida do sistema
    if (!firstSegment || nonProfilePaths.includes(firstSegment)) {
      return false;
    }
    
    // Não é perfil se tem mais de uma barra (além da inicial)
    // Ex: /explore/tags/, /accounts/login/, etc.
    const pathSegments = pathname.split('/').filter(segment => segment !== '');
    if (pathSegments.length > 1) {
      return false;
    }
    
    // Valida se segue o padrão de username do Instagram
    // Usernames podem ter letras, números, pontos e underscores
    return /^[a-zA-Z0-9_.]+$/.test(firstSegment);
  };
  
  return {
    fullUrl: url,
    pathname: pathname,
    isDirectMessage: pathname.includes('/direct/'),
    isProfile: pathname.startsWith('/p/') || isUserProfile(),
    isExplore: pathname.includes('/explore/'),
    isReels: pathname.includes('/reels/'),
    isStories: pathname.includes('/stories/'),
    conversationId: pathname.includes('/direct/t/') ? pathname.split('/direct/t/')[1]?.split('/')[0] : null,
    username: pathname.includes('/direct/t/') ? null : (isUserProfile() ? firstSegment : null)
  };
}

/**
 * Atualiza o contexto baseado na URL atual
 */
function updateContextFromUrl(urlInfo) {
  console.log('URL do Instagram atualizada:', urlInfo.fullUrl);
  // Função simplificada - apenas log da mudança de URL
  // O contexto agora é gerenciado pelo CRM via service Angular
}

/**
 * Atualiza o iframe do PromoKit com informações da URL atual
 */
function updatePromoKitIframe(urlInfo) {
  const iframe = document.getElementById('promokit-iframe');
  if (!iframe) {
    console.log('Iframe não encontrado para atualização');
    return;
  }

  try {
    // Obtém a URL base do iframe atual
    const currentSrc = iframe.src;
    const baseUrl = currentSrc.split('/').slice(0, 3).join('/'); // https://dominio.com
    let newSrc = currentSrc;
    let shouldUpdate = false;

    // Determina a nova URL com base no tipo de página do Instagram
    if (urlInfo.isProfile && urlInfo.username && !urlInfo.isDirectMessage) {
      // Página de perfil - só atualiza se for um username diferente
      if (configIG.currentUsername !== urlInfo.username) {
        newSrc = `${baseUrl}/crm/home/<USER>
        configIG.currentUsername = urlInfo.username;
        shouldUpdate = true;
        console.log('Mudança detectada para novo perfil:', urlInfo.username);
      } else {
        console.log('Mesmo perfil, não atualizando iframe:', urlInfo.username);
      }
    } else if (urlInfo.isDirectMessage) {
      // Página de mensagens diretas - só atualiza se não estava em DM
      if (configIG.currentUsername !== 'direct_messages') {
        newSrc = `${baseUrl}/chat/instagram`;
        configIG.currentUsername = 'direct_messages';
        shouldUpdate = true;
        console.log('Mudança detectada para mensagens diretas');
      }
    }
    // Para outras páginas (/explore/, /reels/, etc.), mantém iframe inalterado
    // Não força mudança para /admin/index

    // Só atualiza se necessário e se a URL for diferente
    if (shouldUpdate && iframe.src !== newSrc) {
      console.log('Recriando iframe com nova URL de:', iframe.src, 'para:', newSrc);

      // Ao invés de modificar iframe.src, recria o iframe completo para evitar erro de permissão
      const container = iframe.parentElement;
      const oldIframe = iframe;

      // Cria novo iframe
      const newIframe = document.createElement('iframe');
      newIframe.id = 'promokit-iframe';
      newIframe.src = newSrc;
      newIframe.style.width = '100%';
      newIframe.style.height = '100%';
      newIframe.style.border = 'none';
      newIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
      newIframe.allowFullscreen = true;

      // Remove o iframe antigo e adiciona o novo
      container.removeChild(oldIframe);
      container.appendChild(newIframe);
    }

  } catch (error) {
    console.log('Erro ao atualizar iframe:', error);
  }
}

// 2. Mensagens da página → background (se necessário)
window.addEventListener('message', (e) => {
  if (!e.data || !e.data.tipo) return;

  console.log('Mensagem recebida na página:', e.data);

  // Tratamento para evento NOVA_MENSAGEM com REQUEST_INSTAGRAM_DATA
  if (e.data.tipo === 'NOVA_MENSAGEM' && e.data.text && e.data.text.tipo === 'REQUEST_INSTAGRAM_DATA') {
    console.log('Solicitação de dados do Instagram via NOVA_MENSAGEM recebida:', e.data.text);
    
    if (e.data.text.username) {
      // 1. Busca spans que contenham exatamente "mais"
      const spans = document.querySelectorAll('header span');
      const spansComMais = [...spans].filter(el => {
        console.log('Texto do span:', el.textContent.trim());
        return el.textContent.trim() === 'mais';
      });

      function getDirectText(element) {
        let text = '';
        element.childNodes.forEach(node => {
          if (node.nodeType === Node.TEXT_NODE) {
            text += node.textContent.trim();
          }
        });
        return text;
      }

      // 2. Busca divs que contenham "e mais"
      const divs = document.querySelectorAll('header div');
      const divsComEMais = [...divs].filter(el => {
        const texto = getDirectText(el).trim().toLowerCase();
        console.log('Texto da div:', texto);
        return texto.includes('e mais');
      });

      // 3. Combina os elementos encontrados
      const elementosParaClicar = [...spansComMais, ...divsComEMais];

      console.log(elementosParaClicar);

      console.log('Elementos encontrados para clicar:', elementosParaClicar.length);

      // 4. Clica em todos os elementos encontrados
      for (const elemento of elementosParaClicar) {
        console.log('Clicando no elemento:', elemento.textContent.trim());
        elemento.click();
      }

      setTimeout( () => {
        let textoInsta = document.querySelector('header').innerText;

        const dialog = document.querySelector('[role="dialog"]');

        if( dialog ) {
          textoInsta += '\nLinks: ' + dialog.innerText;
        }

        const data = {
          tipo: 'INSTAGRAM_DATA_RESPONSE',
          username: e.data.text.username,
          textoInsta: textoInsta
        };
        // Envia dados para o iframe PromoKit
        const iframe = document.getElementById('promokit-iframe');
        if (iframe && iframe.contentWindow) {
          try {
            iframe.contentWindow.postMessage(data, '*');
            console.log('Dados enviados para iframe PromoKit:', data);
          } catch (error) {
            console.error('Erro ao enviar dados para iframe PromoKit:', error);
          }
        } else {
          console.warn('Iframe PromoKit não encontrado ou não carregado');
        }
      }, 500);

    }
  }
});


console.log('Instagram content-script carregado e bridge estabelecida.');

/**
 * Aguarda o carregamento básico do Instagram (existência do main ou header)
 */
function waitForInstagramLoad() {
  return new Promise((resolve) => {
    const isLoaded = () => {
      return document.querySelector('[role="main"], header, nav');
    };

    if (isLoaded()) {
      resolve();
      return;
    }

    const interval = setInterval(() => {
      if (isLoaded()) {
        clearInterval(interval);
        resolve();
      }
    }, 1000);

    // Fallback para seguir em frente após 30 s mesmo se não detectar (evita travar)
    setTimeout(() => {
      clearInterval(interval);
      resolve();
    }, 30000);
  });
}

/**
 * Injeta o iframe do PromoKit na interface do Instagram
 */
function injectInstagramIframe() {
  if (configIG.iframeInjected) return;

  // Cria o container principal
  const container = document.createElement('div');
  container.id = 'promokit-iframe-container';
  container.className = 'promokit-iframe-container';

  // Inicia com z-index baixo para não cobrir diálogos
  container.style.zIndex = '99';

  // Cria botão de configurações
  const settingsBtn = document.createElement('button');
  settingsBtn.className = 'whatsapp-support-iframe-settings-btn';
  settingsBtn.title = 'Configurar URL do iframe';
  settingsBtn.innerHTML = '<svg viewBox="0 0 24 24"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23-0.41-0.12-0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>';
  settingsBtn.addEventListener('click', () => {
    const iframe = document.getElementById('promokit-iframe');
    if (iframe) {
      // Recria o iframe para evitar erro de permissão
      const container = iframe.parentElement;
      if (container) {
        // Remove o iframe existente
        container.removeChild(iframe);

        // Cria um novo iframe com a URL de configurações
        const newIframe = document.createElement('iframe');
        newIframe.id = 'promokit-iframe';
        newIframe.src = chrome.runtime.getURL('iframe-settings.html');
        newIframe.style.width = '100%';
        newIframe.style.height = '100%';
        newIframe.style.border = 'none';
        newIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
        newIframe.allowFullscreen = true;

        // Adiciona o novo iframe ao container
        container.appendChild(newIframe);
      }
    }
  });
  container.appendChild(settingsBtn);

  // Cria iframe interno
  const iframe = document.createElement('iframe');
  iframe.id = 'promokit-iframe';
  iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
  iframe.allowFullscreen = true;

  chrome.storage.local.get(['iframeCustomUrl'], (result) => {
    iframe.src = result.iframeCustomUrl || chrome.runtime.getURL('iframe-settings.html');
    
    // Depois que o iframe carrega, envia a URL atual
    iframe.onload = () => {
      setTimeout(() => {
        const urlInfo = analyzeInstagramUrl(location.href);
        //updatePromoKitIframe(urlInfo);
      }, 1000);
    };
  });

  iframe.style.width = '100%';
  iframe.style.height = '100%';
  iframe.style.border = 'none';
  container.appendChild(iframe);

  // Resizer para ajustar largura
  const resizer = document.createElement('div');
  resizer.id = 'promokit-iframe-resizer';
  resizer.className = 'promokit-iframe-resizer';
  resizer.title = 'Arraste para ajustar a largura do painel';
  container.appendChild(resizer);

  // Aplica estilos principais via JS para evitar dependência extra de CSS
  const styleEl = document.createElement('style');
  styleEl.textContent = `
    .promokit-iframe-container {
      position: fixed;
      top: 0;
      right: 0;
      width: 400px;
      height: 100%;
      background: #ffffff;
      border-left: 1px solid #ccc;
      box-shadow: 0 0 4px rgba(0,0,0,0.3);
      transition: width 0.2s ease;
    }
    .promokit-iframe-resizer {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background: transparent;
    }
    .promokit-iframe-resizer:hover {
      background: rgba(0,0,0,0.1);
    }
  `;
  document.head.appendChild(styleEl);

  // Adiciona lógica de resize
  addResizeLogicInstagram(container, resizer);

  // Adiciona ao DOM
  document.body.appendChild(container);

  configIG.iframeInjected = true;
  console.log('Iframe PromoKit injetado no Instagram');
}

/**
 * Lógica de redimensionamento (adaptada)
 */
function addResizeLogicInstagram(container, resizer) {
  let isResizing = false;
  let startX = 0;
  let startWidth = 0;
  const minWidth = 150;
  const maxWidth = 800;

  resizer.addEventListener('mousedown', (e) => {
    if (e.button !== 0) return;
    isResizing = true;
    startX = e.clientX;
    startWidth = parseInt(document.defaultView.getComputedStyle(container).width, 10);
    document.body.style.userSelect = 'none';
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
    e.preventDefault();
  });

  function onMouseMove(e) {
    if (!isResizing) return;
    const diffX = startX - e.clientX;
    let newWidth = startWidth + diffX;
    newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
    container.style.width = `${newWidth}px`;
    updateInstagramLayout(container);
  }

  function onMouseUp() {
    if (!isResizing) return;
    isResizing = false;
    document.body.style.userSelect = '';
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    const finalWidth = parseInt(container.style.width, 10);
    localStorage.setItem('instagramSupportIframeWidth', finalWidth.toString());
  }

  // Aplica largura salva
  const saved = localStorage.getItem('instagramSupportIframeWidth');
  if (saved) {
    const w = Math.max(minWidth, Math.min(parseInt(saved, 10), maxWidth));
    container.style.width = `${w}px`;
  }
  updateInstagramLayout(container);
}

/**
 * Ajusta o layout da página para acomodar o iframe
 */
function updateInstagramLayout(container) {
  const width = parseInt(container.style.width || '400', 10);
  // Ajusta margem do body para não cobrir conteúdo
  document.body.style.setProperty('margin-right', `${width}px`, 'important');
}

/**
 * Processo de inicialização geral
 */
async function initializeInstagramSupport() {
  if (configIG.initialized) return;
  
  await waitForInstagramLoad();
  
  // Configura detector de URL antes de injetar iframe
  setupUrlChangeDetector();
  
  // Injeta iframe
  injectInstagramIframe();
  
  // Envia URL inicial após tudo estar configurado
  setTimeout(() => {
    const urlInfo = analyzeInstagramUrl(location.href);
    updatePromoKitIframe(urlInfo);
  }, 2000);
  
  configIG.initialized = true;
}

// Inicia após carregar script
initializeInstagramSupport(); 